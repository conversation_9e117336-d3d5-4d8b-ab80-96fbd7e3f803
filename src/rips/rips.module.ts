// src/rips/rips.module.ts
// import { Module } from '@nestjs/common';
// import { HttpModule } from '@nestjs/axios';
// import { RipsService } from './rips.service';
// import { RipsController } from './rips.controller';
// import { PrismaModule } from '../prisma/prisma.module'; // ✅ Importar PrismaModule

// @Module({
//   imports: [HttpModule, PrismaModule], // ✅ Agregar aquí
//   controllers: [RipsController],
//   providers: [RipsService],
// })
// export class RipsModule {}

// src/rips/rips.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { RipsService } from './rips.service';
import { RipsController } from './rips.controller';

@Module({
  imports: [
    HttpModule,
    ConfigModule.forRoot({
      isGlobal: true, // hace que ConfigService esté disponible globalmente
    }),
  ],
  controllers: [RipsController],
  providers: [RipsService],
})
export class RipsModule {}
