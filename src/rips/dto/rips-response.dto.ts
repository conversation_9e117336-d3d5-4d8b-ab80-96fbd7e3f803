// src/rips/rips-response.dto.ts
export class RipsResponseDto {
  ID_SERVICIO: number;
  ID_CLIENTE: number;
  TIPODOCUMENTOIDENTIFICACION: string;
  NUMDOCUMENTOIDENTIFICACION: string;
  TIPOUSUARIO: number;
  FECHANACIMIENTO: string;
  CODSEXO: string;
  CODPAISRESIDENCIA: string | null;
  CODZONATER<PERSON>TORIALRESIDENCIA: string;
  INCAPACIDAD: string | null;
  CODPAISORIGEN: string | null;
  CODPRESTADOR: string | null;
  FECHALNICIOATENCION: string | null;
  NUMAUTORIZACION: string | null;
  CODCONSULTA: string | null;
  MOD<PERSON><PERSON><PERSON>GRUPOSERVICIOTECSAL: string | null;
  CAUSAMOTIVOATENCION: string | null;
  CODDIAGNOSTICOPRINCIPAL: string;
  CODDIAGNOSTICORELACIONADO1: string;
  CODDIAGNOSTICORELACIONADO2: string;
  CODDIAGNOSTICORELACIONADO3: string;
  TIPODOCUMENTOIDENTIFICACIONDOC: string | null;
  FEC<PERSON>_DE_SERVICIO: string;
  FECHA_INICIO_TRAMITE: string;
  ESTADO_SERVICIO: string;
}
