// src/rips/rips.service.ts
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as FormData from 'form-data';

@Injectable()
export class RipsService {
  constructor(private readonly httpService: HttpService) {}

  async enviarFacturaConRips(): Promise<any> {
    const form = new FormData();

    const data = {
      informacion_documento: {
        cufe_cude:
          'be27ab71fea9c9fd8c01231235d221115d063a9be5b618d97597cb5792d86f857fe12345667890ca99f3613d3f6b7a97af',
        fecha_documento: '2025-01-30',
        numero_documento: 'SETT28',
      },
      generalidades: {
        tipo_ambiente_dian: 2,
        identificador_transmision: '20250130',
        integrador: {
          nombre: 'WEB',
          tipo: 'WEB',
        },
      },
    };

    const jsonRips = {
      numDocumentoIdObligado: '11223344',
      numFactura: 'SETT28',
      tipoNota: null,
      numNota: null,
      usuarios: [
        {
          tipoDocumentoIdentificacion: 'CC',
          numDocumentoIdentificacion: '10101010101',
          tipoUsuario: '01',
          fechaNacimiento: '1990-08-02',
          codSexo: 'F',
          codPaisResidencia: '170',
          codMunicipioResidencia: '41001',
          codZonaTerritorialResidencia: '02',
          incapacidad: 'NO',
          consecutivo: 1,
          codPaisOrigen: '170',
          servicios: {
            consultas: [
              {
                codPrestador: '111111111111',
                fechaInicioAtencion: '2025-01-22 13:20',
                numAutorizacion: '2000272',
                codConsulta: '890242',
                modalidadGrupoServicioTecSal: '01',
                grupoServicios: '01',
                codServicio: 308,
                finalidadTecnologiaSalud: '44',
                causaMotivoAtencion: '38',
                codDiagnosticoPrincipal: 'B86X',
                codDiagnosticoRelacionado1: null,
                codDiagnosticoRelacionado2: null,
                codDiagnosticoRelacionado3: null,
                tipoDiagnosticoPrincipal: '01',
                tipoDocumentoIdentificacion: 'CC',
                numDocumentoIdentificacion: '7691730',
                vrServicio: 149000,
                conceptoRecaudo: '03',
                valorPagoModerador: 87000,
                numFEVPagoModerador: null,
                consecutivo: 1,
              },
            ],
          },
        },
      ],
    };

    // Llenar el FormData
    form.append('documento_obligado', '900810402');
    form.append('data', JSON.stringify(data));
    form.append('json_rips', JSON.stringify(jsonRips));

    const token = process.env.TOKEN_AFAC || 'TU_TOKEN_AQUI';

    const headers = {
      ...form.getHeaders(),
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    };

    const url =
      'https://servicios-pruebas.afacturar.com/api/rips/cargar_fe_rips';

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, form, { headers }),
      );
      return {
        status: 'OK',
        data: response.data,
      };
    } catch (error) {
      return {
        status: 'ERROR',
        code: error.response?.status,
        data: error.response?.data || error.message,
      };
    }
  }
}
