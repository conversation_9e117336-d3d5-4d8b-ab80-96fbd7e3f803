// src/afacturar_token/afacturar-token.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AfacturarTokenService } from './afacturar-token.service';
import { AfacturarTokenController } from './afacturar-token.controller';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [HttpModule, PrismaModule],
  controllers: [AfacturarTokenController],
  providers: [AfacturarTokenService],
})
export class AfacturarTokenModule {}
