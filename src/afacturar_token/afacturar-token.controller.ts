// src/afacturar_token/afacturar-token.controller.ts
import { Controller, Post } from '@nestjs/common';
import { AfacturarTokenService } from './afacturar-token.service';

@Controller('afacturar-token')
export class AfacturarTokenController {
  constructor(private readonly tokenService: AfacturarTokenService) {}

  @Post()
  async probarConexion() {
    return await this.tokenService.obtenerToken();
  }
}
