// src/afacturar_token/afacturar-token.service.ts
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AfacturarTokenService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async obtenerToken() {
    const url = 'https://servicios-pruebas.afacturar.com/api/token_obligado';

    const data = {
      documento_obligado: this.configService.get('AFACTURAR_DOCUMENTO'),
      usuario: this.configService.get('AFACTURAR_USUARIO'),
      contrasena: this.configService.get('AFACTURAR_CONTRASENA'),
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, data, {
          headers: { 'Content-Type': 'application/json' },
        }),
      );

      const { codigo_error, descripcion_error, resultado } = response.data;

      // Si es una respuesta 200 pero contiene errores funcionales
      if (codigo_error) {
        const resultadoPlano = Array.isArray(resultado)
          ? resultado
              .map((item) => (Array.isArray(item) ? item.join(', ') : item))
              .join(' | ')
          : '';

        await this.prisma.apiLog.create({
          data: {
            code: codigo_error,
            status: 'Error funcional',
            description: `${descripcion_error}. Detalle: ${resultadoPlano}`,
          },
        });
      }

      return response.data;
    } catch (error) {
      const code = error.response?.status || 500;
      const body = error.response?.data || error.message;

      let status = 'Error';
      let description = '';

      if (typeof body === 'object') {
        const { descripcion_error, resultado } = body;

        const resultadoPlano = Array.isArray(resultado)
          ? resultado
              .map((item) => (Array.isArray(item) ? item.join(', ') : item))
              .join(' | ')
          : '';

        status = body?.message || 'Error HTTP';
        description = descripcion_error
          ? `${descripcion_error}. Detalle: ${resultadoPlano}`
          : JSON.stringify(body);
      } else {
        description = typeof body === 'string' ? body : 'Unknown error';
      }

      await this.prisma.apiLog.create({
        data: {
          code,
          status,
          description,
        },
      });

      throw error;
    }
  }
}
