import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AfacturarTokenModule } from './afacturar_token/afacturar-token.module';
import { RipsModule } from './rips/rips.module';
import { PrismaModule } from './prisma/prisma.module'; // ✅ importa el módulo aquí
import { FacturaNacionalModule } from './factura_nacional/factura-nacional.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    HttpModule,
    PrismaModule,
    AfacturarTokenModule,
    RipsModule,
    FacturaNacionalModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
