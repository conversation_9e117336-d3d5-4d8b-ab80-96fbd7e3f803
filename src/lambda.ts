import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Hand<PERSON> } from 'aws-lambda';
import serverlessExpress from '@vendia/serverless-express';
import { ExpressAdapter } from '@nestjs/platform-express';
import express from 'express';

let cachedServer: Handler;

async function bootstrap() {
  const expressApp = express();
  const app = await NestFactory.create(
    AppModule,
    new ExpressAdapter(expressApp),
  );
  
  // Configuración de CORS si es necesario
  app.enableCors();
  
  await app.init();
  
  return serverlessExpress({
    app: expressApp,
  });
}

export const handler: Handler = async (event, context) => {
  if (!cachedServer) {
    cachedServer = await bootstrap();
  }
  return cachedServer(event, context);
};