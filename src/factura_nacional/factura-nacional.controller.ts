// src/factura_nacional/factura-nacional.controller.ts
import { Controller, Post } from '@nestjs/common';
import { AfacturarFacturaService } from './factura-nacional.service';

@Controller('factura-nacional')
export class FacturaNacionalController {
  constructor(private readonly facturaService: AfacturarFacturaService) {}

  @Post()
  async enviarFactura() {
    return await this.facturaService.enviarFacturaFormData(); // usa el método correcto
  }
}
