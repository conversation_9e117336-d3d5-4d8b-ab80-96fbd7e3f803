// src/factura_nacional/factura-nacional.ts
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from '../prisma/prisma.service';
import * as FormData from 'form-data';

@Injectable()
export class AfacturarFacturaService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async enviarFacturaFormData() {
    const url =
      'https://servicios-pruebas.afacturar.com/api/vp/factura_nacional';
    const token = this.configService.get<string>('AFACTURAR_TOKEN');
    const documento_obligado = '900810402'; // o desde ConfigService
    const today = new Date();
    const currentDate = today.toISOString().split('T')[0];
    const currentHour = today.toTimeString().split(' ')[0];

    const expirationDateObj = new Date(
      today.getTime() + 30 * 24 * 60 * 60 * 1000,
    );
    const expirationDate = expirationDateObj.toISOString().split('T')[0];

    const firstDayPreviousMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1,
    );
    const lastDayPreviousMonth = new Date(
      today.getFullYear(),
      today.getMonth(),
      0,
    );

    const startDate = firstDayPreviousMonth.toISOString().split('T')[0];
    const endDate = lastDayPreviousMonth.toISOString().split('T')[0];

    // ⬇️ AGREGA AQUÍ ESTE BLOQUE
    const facturasPendientes = await this.prisma.facturaNacional.findMany({
      where: {
        estado_envio: 'PENDIENTE',
      },
      include: {
        informacionAdquiriente: true,
      },
    });

    if (!facturasPendientes.length) {
      console.log('✅ No hay facturas con estado PENDING');
    } else {
      console.log('📋 Facturas con estado PENDIENTE:');
      facturasPendientes.forEach((factura) => {
        console.log({
          id: factura.id,
          id_factura: factura.id_factura,
          informacionAdquirienteId: factura.informacionAdquirienteId,
          estado_envio: factura.estado_envio,
        });
      });
    }

    const facturaJson = {
      facturas: [
        {
          encabezado: {
            id_factura: '46',
            fecha: currentDate, // variable today
            hora: currentHour, // today hours
            nota: ["{'son':'','son_total_a_pagar':''}"],
            moneda: 'COP', // QUEMADO
            tipo_factura: 10, // QUEMADO
            tipo_de_pago: 47, // QUEMADO
            numero_resolucion_facturacion: '18760000001', // QUEMADO
            fecha_vencimiento: expirationDate, // Crédito 30 días calendario apartir de la fecha de facturación o fecha actual
            numero_orden: '',
            prefijo: 'SETT', // QUEMADO
            metodo_de_pago: 2, // QUEMADO
            identificador_de_pago: '',
          },
          informacion_adquiriente: {
            tipo_contribuyente: 1,
            tipo_regimen: 2,
            tipo_identificacion: 31, // QUEMADO en base los datos del cliente
            identificacion: '891800330', // QUEMADO en base los datos del cliente
            correo_electronico: '<EMAIL>', // QUEMADO en base los datos del cliente
            numero_movil: '3183010101', // QUEMADO en base los datos del cliente
            nombre: {
              razon_social: 'UNIVERSIDAD PEDAGOGICA Y TECNOLOGICA DE COLOMBIA', // QUEMADO en base los datos del cliente
              primer_nombre: '',
              segundo_nombre: '',
              apellido: '',
            },
            departamento: '15', // Manual - Colombia.json // QUEMADO en base los datos del cliente
            zona: '',
            ciudad: '15001', // Manual - Colombia.json // QUEMADO en base los datos del cliente
            direccion: 'CR 6 39 115 AV CENTRAL DEL NORTE', // QUEMADO en base los datos del cliente
            pais: 'CO',
            RUT: {
              resp_calidades_atributos: ['R-99-PN'],
              usuario_aduanero: [''],
            },
            nombre_ciudad: 'TUNJA', // Manual - Colombia.json // QUEMADO en base los datos del cliente
            nombre_departamento: 'BOYACA', // Manual - Colombia.json // QUEMADO en base los datos del cliente
            nombre_pais: 'Colombia',
          },
          detalle_factura: [
            {
              numero_linea: 1,
              cantidad: 1, // Números según lo que reporta el cliente
              unidad_de_cantidad: '94',
              nombre_unidad_medida: 'unidad',
              valor_unitario: '35466.00',
              descripcion: 'CONSULTA MEDICO LABORAL PRESENCIAL – TELEMEDICINA',
              nota_detalle: '',
              regalo: {
                es_regalo: false, // NO aplica
                cod_precio_referencia: 0,
                precio_referencia: '0.00',
              },
              cargo_descuento: {
                es_descuento: false, // NO aplica
                porcentaje_cargo_descuento: '0.00',
                valor_base_cargo_descuento: '0.00',
                valor_cargo_descuento: '0.00',
              },
              marca: '',
              modelo: '',
              retenciones_detalle: [
                {
                  codigo: 0, // Depende de contabilidad para que aparezca
                  porcentaje: '0.00',
                  valor_base: '0.00',
                  valor_retenido: '0.00',
                },
              ],
              valores_unitarios: {
                valor_descuento: '0.00',
                valor_con_descuento: '35466.00',
                valor_impuesto_1: '6738.00', // iva
                valor_impuesto_2: '0.00',
                valor_impuesto_3: '0.00',
                valor_impuesto_4: '0.00',
                valor_reteiva: '0.00', // iva * 15% reteiva, 15% del valor del iva  float
                valor_retefuente: '0.00', // descuento total sobre el precio ReteFuente float
                valor_reteica: '0.00', // descuento total sobre el precio ReteFuente // Crear tabla para mí 0.00966 // aproximado
                valor_a_pagar: '0.00', // floatvalor_con_descuento + valor_impuesto_1 - valor_reteiva "si aplica" - valor_retefuente "si aplica" - valor_reteica "si aplica"
              },
              valor_total_detalle_con_cargo_descuento: '35466.00', // cantidad * valor unitario
              valor_total_detalle: '35466.00', // cantidad * valor unitario
              impuestos_detalle: {
                codigo_impuesto: 1,
                porcentaje_impuesto: '19.00',
                valor_base_impuesto: '35466.00',
                valor_impuesto: '6738.00',
              },
            },
          ],
          impuestos: [
            {
              codigo_impuesto: 1,
              porcentaje_impuesto: '19.00', // porcentage sin porcentaje y float
              valor_base_calculo_impuesto: '35466.00', // valor base
              valor_total_impuesto: '6738.00', // valor del iva
            },
          ],
          recargos: [
            {
              nombre_recargo: '', //No aplica
              porcentaje_recargo: '0.00',
              valor_base_calculo_recargo: '0.00',
              valor_total_recargo: '0.00',
            },
          ],
          valor_factura: {
            valor_base: '35466.00',
            valor_base_calculo_impuestos: '35466.00',
            valor_anticipo: '0.00',
            valor_total_impuesto_1: '6738.00',
            valor_total_impuesto_2: '0.00',
            valor_total_impuesto_3: '0.00',
            valor_total_impuesto_4: '0.00',
            total_factura: '42204.00', // Total base  + iva
            valor_total_recargos: '0.00',
            valor_base_mas_impuestos: '42204.00',
            valor_total_reteiva: '0.00',
            valor_total_retefuente: '0.00',
            valor_total_reteica: '0.00',
            valor_descuento_total: '0.00', // No aplica
            valor_total_a_pagar: '42204.00',
          },
          retenciones: [
            {
              codigo: 0,
              porcentaje: '0.00',
              valor_base: '0.00',
              valor_retenido: '0.00',
            },
          ],
          descuentos: [
            // Si aplica arriba en Detalle factura
            {
              codigo_descuento: 99,
              porcentaje_descuento: '0.00',
              valor_base_calculo_descuento: '0.00',
              valor_total_descuento: '0.00',
            },
          ],
          sector: {
            tipo: 'SALUD',
            tipo_operacion: 'SS-CUFE',
            coleccion: [
              {
                nombre: 'usuario',
                informacion_adicional: [
                  {
                    variable: 'CODIGO_PRESTADOR',
                    valor: '1100130475', // Código real
                  },
                  {
                    variable: 'MODALIDAD_PAGO',
                    valor: '5',
                  },
                  {
                    variable: 'COBERTURA_PLAN_BENEFICIOS',
                    valor: '12',
                  },
                  {
                    variable: 'NUMERO_CONTRATO',
                    valor: '59',
                  },
                  {
                    variable: 'NUMERO_POLIZA',
                    valor: '',
                  },
                  {
                    variable: 'COPAGO',
                    valor: '0',
                  },
                  {
                    variable: 'CUOTA_MODERADORA',
                    valor: '0',
                  },
                  {
                    variable: 'CUOTA_RECUPERACION',
                    valor: '0',
                  },
                  {
                    variable: 'PAGOS_COMPARTIDOS',
                    valor: '0',
                  },
                ],
              },
            ],
            interoperatividad: {
              url_descarga_adjuntos: {
                url: '',
                parametros: [],
              },
              entrega_documento: {
                ws: '',
                parametros: [],
              },
            },
          },
          periodo_facturacion: {
            fecha_inicio: startDate, // Mes de inicio anterior todavia month
            fecha_fin: endDate, // Fin del mes anterior todavia month
          },
        },
      ],
      generalidades: {
        tipo_ambiente_dian: 2, // QUEMADO
        rg_modelo: '', // QUEMADO
        rg_moneda: '', // QUEMADO
        version: 2, // QUEMADO
        identificador_transmision: 'REN CONSULTORES', // QUEMADO
        notificacion: {
          es_automatico: 'N', // QUEMADO
          correo_obligado: '', // QUEMADO
          asunto: '', // QUEMADO
          con_copia: '', // QUEMADO
        },
        integrador: {
          nombre: 'WEB', // QUEMADO
          tipo: 'WEB', // QUEMADO
        },
      },
    };

    const form = new FormData();
    form.append('documento_obligado', documento_obligado);
    form.append('data', JSON.stringify(facturaJson));

    const headers = {
      ...form.getHeaders(),
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, form, { headers }),
      );

      const data = response.data;
      const resultado =
        typeof data.resultado === 'object' && !Array.isArray(data.resultado)
          ? data.resultado
          : {};

      const mensajes = Array.isArray(data.resultado)
        ? data.resultado.flat(2).map((r) => `${r.propiedad}: ${r.descripcion}`)
        : [];

      const detalle_error = mensajes.join(' | ') || null;

      const status = data.codigo_error === 0 ? 'SUCCESS' : 'VALIDATE'; // Cambia el estado si hubo respuesta pero con errores semánticos ejemplo code 400

      await this.prisma.apiLog.create({
        data: {
          code: data.codigo_error,
          status,
          description: data.descripcion_error,
          id_factura: resultado.id_factura ?? null,
          cufe: resultado.cufe ?? null,
          pdf_url: resultado.url_representacion_grafica ?? null,
          qr_svg: resultado.url_representacion_qr_svg ?? null,
          qr_base64: resultado.qr_url_imagen_base_64 ?? null,
          xml_url: resultado.url_representacion_xml ?? null,
          app_response_url: resultado.url_application_response ?? null,
          doc_url: resultado.url_attached_document_url ?? null,
          zip_url: resultado.url_attached_document_zip ?? null,
          fecha_dian: resultado.fecha_validacion_dian ?? null,
          entorno: data.entorno ?? null,
          error_message_dian: data.ErrorMessage_DIAN ?? detalle_error,
          transaccion: data.transaccion ?? null,
          tiempo_ejecucion: data.tiempo_ejecucion ?? null,
          detalle_error,
        },
      });

      return data;
    } catch (error) {
      const err = error.response?.data ?? {};
      const resultadoArray = Array.isArray(err.resultado)
        ? err.resultado.flat(2)
        : [];

      const detalle_error =
        resultadoArray
          .map((r) => `${r.propiedad}: ${r.descripcion}`)
          .join(' | ') || null;

      await this.prisma.apiLog.create({
        data: {
          code: err.codigo_error || 500,
          status: 'ERROR',
          description: err.descripcion_error || error.message,
          id_factura: detalle_error?.match(/SETT\\d+/)?.[0] ?? null,
          cufe: null,
          pdf_url: null,
          qr_svg: null,
          qr_base64: null,
          xml_url: null,
          app_response_url: null,
          doc_url: null,
          zip_url: null,
          fecha_dian: null,
          entorno: err.entorno ?? null,
          error_message_dian: err.ErrorMessage_DIAN ?? detalle_error,
          transaccion: err.transaccion ?? null,
          tiempo_ejecucion: err.tiempo_ejecucion ?? null,
          detalle_error,
        },
      });

      throw error;
    }
  }
}
