FROM node:22-alpine

WORKDIR /app

# Copiar solo los archivos necesarios para la instalación de dependencias
COPY package.json package-lock.json ./

# Copiar el esquema de Prisma antes de instalar
COPY src/prisma ./src/prisma/

# Instalar dependencias
RUN npm install

# Copiar el resto de los archivos del proyecto
COPY . .

# Generar el cliente Prisma explícitamente después de copiar todos los archivos
RUN npx prisma generate --schema=./src/prisma/schema.prisma

EXPOSE 3000

CMD ["npm", "run", "start:dev"]

