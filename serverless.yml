service: admin-backend-rips

frameworkVersion: '3'

provider:
  name: aws
  region: us-east-2  # Ajusta a la región donde está tu RDS
  ecr:
    # Habilita el soporte para imágenes ECR
    images:
      appimage:
        path: ./  # Ruta al Dockerfile

functions:
  api:
    image:
      name: appimage  # Referencia a la imagen definida arriba
      command:
        - node
        - dist/main.js
    events:
      - httpApi:
          path: /{proxy+}
          method: any
    environment:
      # Variables de entorno para la función Lambda
      NODE_ENV: production
      # Las variables sensibles se cargarán desde SSM Parameter Store
      DATABASE_URL: ${ssm:/admin-backend-rips/database-url}
    timeout: 30  # Tiempo máximo de ejecución en segundos
    memorySize: 1024  # Memoria asignada en MB

plugins:
  - serverless-offline  # Para pruebas locales
  - serverless-dotenv-plugin  # Para cargar variables desde .env durante el desarrollo

custom:
  dotenv:
    path: ./.env  # Ruta al archivo .env