FROM public.ecr.aws/lambda/nodejs:22

# Establecer directorio de trabajo
WORKDIR ${LAMBDA_TASK_ROOT}

# Copiar archivos de configuración
COPY package*.json ./
COPY src/prisma ./src/prisma/

# Instalar dependencias
RUN npm install

# Copiar el código fuente
COPY . .

# Generar el cliente Prisma
RUN npx prisma generate --schema=./src/prisma/schema.prisma

# Compilar la aplicación
RUN npm run build

# Comando para ejecutar la aplicación
CMD ["dist/main.handler"]